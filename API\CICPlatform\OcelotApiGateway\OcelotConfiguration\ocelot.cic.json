{"Routes": [{"DownstreamPathTemplate": "/api/{version}/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5003}], "UpstreamPathTemplate": "/cic-gate/{version}/{everything}", "UpstreamHttpMethod": ["Get", "Post", "Put", "Delete"], "SwaggerKey": "cic", "AuthenticationOptions": {"AuthenticationProviderKey": "Bearer", "AllowedScopes": ["cic.read", "cic.write"]}, "RouteClaimsRequirement": {"role": "User"}, "RateLimitRule": {"EnableRateLimiting": true, "Period": "1m", "PeriodTimespan": 60, "Limit": 10, "ClientWhitelist": ["trusted-client"]}, "LoadBalancerOptions": {"Type": "RoundR<PERSON>in"}, "QoSOptions": {"ExceptionsAllowedBeforeBreaking": 3, "DurationOfBreak": 10000, "TimeoutValue": 5000}, "FileCacheOptions": {"TtlSeconds": 15, "Region": "cicregion"}, "SecurityOptions": {"IPAllowedList": [], "IPBlockedList": []}, "Priority": 2}]}
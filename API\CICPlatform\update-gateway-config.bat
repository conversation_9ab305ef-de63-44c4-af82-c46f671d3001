@echo off
echo Updating Gateway configuration with current container ports...

REM Get the current ports for Admin and CIC APIs
for /f "tokens=2 delims=:" %%a in ('docker port CICPlatform.Admin.API 8080') do set ADMIN_PORT=%%a
for /f "tokens=2 delims=:" %%a in ('docker port CICPlatform.API 8080') do set CIC_PORT=%%a

echo Admin API is running on port: %ADMIN_PORT%
echo CIC API is running on port: %CIC_PORT%

REM Update the Ocelot configuration files
powershell -Command "(Get-Content 'OcelotApiGateway\OcelotConfiguration\ocelot.admin.json') -replace '\"Port\": \d+', '\"Port\": %ADMIN_PORT%' | Set-Content 'OcelotApiGateway\OcelotConfiguration\ocelot.admin.json'"

powershell -Command "(Get-Content 'OcelotApiGateway\OcelotConfiguration\ocelot.cic.json') -replace '\"Port\": \d+', '\"Port\": %CIC_PORT%' | Set-Content 'OcelotApiGateway\OcelotConfiguration\ocelot.cic.json'"

powershell -Command "(Get-Content 'OcelotApiGateway\OcelotConfiguration\ocelot.SwaggerEndpoints.json') -replace 'host\.docker\.internal:\d+', 'host.docker.internal:%ADMIN_PORT%' | Set-Content 'temp_swagger.json'"

powershell -Command "(Get-Content 'temp_swagger.json') -replace 'host\.docker\.internal:%ADMIN_PORT%/swagger/v1/swagger\.json\"', 'host.docker.internal:%ADMIN_PORT%/swagger/v1/swagger.json\"' | Set-Content 'temp_swagger2.json'"

powershell -Command "(Get-Content 'temp_swagger2.json') -replace 'host\.docker\.internal:%ADMIN_PORT%', 'host.docker.internal:%CIC_PORT%' | Set-Content 'OcelotApiGateway\OcelotConfiguration\ocelot.SwaggerEndpoints.json'"

del temp_swagger.json temp_swagger2.json 2>nul

echo Configuration updated. Restarting Gateway...
docker restart OcelotApiGateway

echo.
echo Gateway should now be accessible at:
for /f "tokens=2 delims=:" %%a in ('docker port OcelotApiGateway 8080') do echo http://localhost:%%a/swagger

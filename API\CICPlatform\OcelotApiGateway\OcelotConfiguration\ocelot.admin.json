{"Routes": [{"DownstreamPathTemplate": "/api/{version}/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "admin-api", "Port": 8080}], "UpstreamPathTemplate": "/admin-gate/{version}/{everything}", "UpstreamHttpMethod": ["Get", "Post", "Put", "Delete"], "SwaggerKey": "admin", "AuthenticationOptions": {"AuthenticationProviderKey": "Bearer", "AllowedScopes": ["admin.read", "admin.write"]}, "RouteClaimsRequirement": {"role": "Admin"}, "DangerousAcceptAnyServerCertificateValidator": true, "RateLimitRule": {"EnableRateLimiting": true, "Period": "1m", "PeriodTimespan": 60, "Limit": 10, "ClientWhitelist": ["trusted-client"]}, "LoadBalancerOptions": {"Type": "RoundR<PERSON>in"}, "QoSOptions": {"ExceptionsAllowedBeforeBreaking": 3, "DurationOfBreak": 10000, "TimeoutValue": 5000}, "FileCacheOptions": {"TtlSeconds": 15, "Region": "adminregion"}, "SecurityOptions": {"IPAllowedList": [], "IPBlockedList": []}, "Priority": 2}, {"DownstreamPathTemplate": "/swagger/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "admin-api", "Port": 8080}], "UpstreamPathTemplate": "/admin-swagger/{everything}", "UpstreamHttpMethod": ["Get", "Options"], "SwaggerKey": "admin", "Priority": 1}]}
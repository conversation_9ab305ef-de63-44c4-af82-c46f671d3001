# PowerShell script to fix Gateway configuration with current container ports

Write-Host "Getting current container ports..." -ForegroundColor Green

# Get the current ports for Admin and CIC APIs
$adminPort = (docker port CICPlatform.Admin.API 8080).Split(':')[1]
$cicPort = (docker port CICPlatform.API 8080).Split(':')[1]
$gatewayPort = (docker port OcelotApiGateway 8080).Split(':')[1]

Write-Host "Admin API is running on port: $adminPort" -ForegroundColor Yellow
Write-Host "CIC API is running on port: $cicPort" -ForegroundColor Yellow
Write-Host "Gateway is running on port: $gatewayPort" -ForegroundColor Yellow

# Update admin configuration
Write-Host "Updating admin configuration..." -ForegroundColor Green
$adminConfig = Get-Content "OcelotApiGateway\OcelotConfiguration\ocelot.admin.json" -Raw
$adminConfig = $adminConfig -replace '"Port": \d+', "`"Port`": $adminPort"
$adminConfig | Set-Content "OcelotApiGateway\OcelotConfiguration\ocelot.admin.json"

# Update CIC configuration
Write-Host "Updating CIC configuration..." -ForegroundColor Green
$cicConfig = Get-Content "OcelotApiGateway\OcelotConfiguration\ocelot.cic.json" -Raw
$cicConfig = $cicConfig -replace '"Port": \d+', "`"Port`": $cicPort"
$cicConfig | Set-Content "OcelotApiGateway\OcelotConfiguration\ocelot.cic.json"

# Update Swagger endpoints
Write-Host "Updating Swagger endpoints..." -ForegroundColor Green
$swaggerConfig = Get-Content "OcelotApiGateway\OcelotConfiguration\ocelot.SwaggerEndpoints.json" -Raw
$swaggerConfig = $swaggerConfig -replace 'host\.docker\.internal:\d+', "host.docker.internal:$adminPort"
$swaggerConfig = $swaggerConfig -replace "(host\.docker\.internal:$adminPort.*?host\.docker\.internal:)\d+", "`$1$cicPort"
$swaggerConfig | Set-Content "OcelotApiGateway\OcelotConfiguration\ocelot.SwaggerEndpoints.json"

# Restart Gateway
Write-Host "Restarting Gateway..." -ForegroundColor Green
docker restart OcelotApiGateway | Out-Null

Start-Sleep -Seconds 5

Write-Host "`nServices are now available at:" -ForegroundColor Green
Write-Host "- Admin API: http://localhost:$adminPort/swagger" -ForegroundColor Cyan
Write-Host "- CIC API: http://localhost:$cicPort/swagger" -ForegroundColor Cyan
Write-Host "- Gateway: http://localhost:$gatewayPort/swagger" -ForegroundColor Cyan

Write-Host "`nTesting Gateway connectivity..." -ForegroundColor Green
try {
    $response = Invoke-WebRequest -Uri "http://localhost:$gatewayPort/health" -TimeoutSec 10
    Write-Host "Gateway health check: OK" -ForegroundColor Green
} catch {
    Write-Host "Gateway health check: FAILED" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

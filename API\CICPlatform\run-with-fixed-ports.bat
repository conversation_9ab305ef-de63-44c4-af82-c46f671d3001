@echo off
echo Stopping existing containers...
docker stop OcelotApiGateway CICPlatform.API CICPlatform.Admin.API 2>nul
docker rm OcelotApiGateway CICPlatform.API CICPlatform.Admin.API 2>nul

echo.
echo Starting containers with fixed ports...

echo Starting Admin API on port 5002...
docker run -d --name CICPlatform.Admin.API ^
  -p 5002:8080 ^
  -e ASPNETCORE_ENVIRONMENT=Development ^
  -e ASPNETCORE_URLS=http://+:8080 ^
  -e ASPNETCORE_HTTPS_PORTS= ^
  --network bridge ^
  cicplatformadminapi:dev

echo Starting CIC API on port 5003...
docker run -d --name CICPlatform.API ^
  -p 5003:8080 ^
  -e ASPNETCORE_ENVIRONMENT=Development ^
  -e ASPNETCORE_URLS=http://+:8080 ^
  -e ASPNETCORE_HTTPS_PORTS= ^
  --network bridge ^
  cicplatformapi:dev

echo Waiting for APIs to start...
timeout /t 10 /nobreak >nul

echo Starting Gateway on port 5004...
docker run -d --name OcelotApiGateway ^
  -p 5004:8080 ^
  -e ASPNETCORE_ENVIRONMENT=Development ^
  -e ASPNETCORE_URLS=http://+:8080 ^
  -e ASPNETCORE_HTTPS_PORTS= ^
  --network bridge ^
  --add-host cicplatform.admin.api:host-gateway ^
  --add-host cicplatform.api:host-gateway ^
  ocelotapigateway:dev

echo.
echo Services should be available at:
echo - Admin API: http://localhost:5002/swagger
echo - CIC API: http://localhost:5003/swagger
echo - Gateway: http://localhost:5004/swagger
echo.
echo Checking container status...
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

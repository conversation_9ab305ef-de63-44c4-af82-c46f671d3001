# Final solution to fix Gateway Swagger issues

Write-Host "=== Final Gateway Fix ===" -ForegroundColor Green

# Get current container ports
$containers = docker ps --format "{{.Names}}\t{{.Ports}}" | ConvertFrom-Csv -Delimiter "`t" -Header "Name", "Ports"

$adminPort = ($containers | Where-Object { $_.Name -eq "CICPlatform.Admin.API" }).Ports -replace ".*:(\d+)->8080.*", '$1'
$cicPort = ($containers | Where-Object { $_.Name -eq "CICPlatform.API" }).Ports -replace ".*:(\d+)->8080.*", '$1'
$gatewayPort = ($containers | Where-Object { $_.Name -eq "OcelotApiGateway" }).Ports -replace ".*:(\d+)->8080.*", '$1'

Write-Host "Current ports:" -ForegroundColor Yellow
Write-Host "  Admin API: $adminPort" -ForegroundColor Cyan
Write-Host "  CIC API: $cicPort" -ForegroundColor Cyan
Write-Host "  Gateway: $gatewayPort" -ForegroundColor Cyan

# Get the host IP address that containers can reach
$hostIP = (Get-NetIPAddress -AddressFamily IPv4 | Where-Object { $_.IPAddress -like "192.168.*" -or $_.IPAddress -like "10.*" -or $_.IPAddress -like "172.*" } | Select-Object -First 1).IPAddress

if (-not $hostIP) {
    $hostIP = "host.docker.internal"
    Write-Host "Using host.docker.internal as fallback" -ForegroundColor Yellow
} else {
    Write-Host "Using host IP: $hostIP" -ForegroundColor Yellow
}

# Create a simple Swagger endpoints configuration
$swaggerConfig = @"
{
  "SwaggerEndPoints": [
    {
      "Key": "admin",
      "Config": [
        {
          "Name": "Admin Web API",
          "Version": "v1",
          "Url": "http://${hostIP}:${adminPort}/swagger/v1/swagger.json"
        }
      ]
    },
    {
      "Key": "cic",
      "Config": [
        {
          "Name": "CIC Web API", 
          "Version": "v1",
          "Url": "http://${hostIP}:${cicPort}/swagger/v1/swagger.json"
        }
      ]
    }
  ]
}
"@

# Update Swagger endpoints
Write-Host "Updating Swagger endpoints configuration..." -ForegroundColor Green
$swaggerConfig | Set-Content "OcelotApiGateway\OcelotConfiguration\ocelot.SwaggerEndpoints.json"

# Update admin routes
Write-Host "Updating admin routes..." -ForegroundColor Green
$adminConfig = Get-Content "OcelotApiGateway\OcelotConfiguration\ocelot.admin.json" -Raw
$adminConfig = $adminConfig -replace '"Host": "[^"]*"', "`"Host`": `"$hostIP`""
$adminConfig = $adminConfig -replace '"Port": \d+', "`"Port`": $adminPort"
$adminConfig | Set-Content "OcelotApiGateway\OcelotConfiguration\ocelot.admin.json"

# Update CIC routes
Write-Host "Updating CIC routes..." -ForegroundColor Green
$cicConfig = Get-Content "OcelotApiGateway\OcelotConfiguration\ocelot.cic.json" -Raw
$cicConfig = $cicConfig -replace '"Host": "[^"]*"', "`"Host`": `"$hostIP`""
$cicConfig = $cicConfig -replace '"Port": \d+', "`"Port`": $cicPort"
$cicConfig | Set-Content "OcelotApiGateway\OcelotConfiguration\ocelot.cic.json"

# Restart Gateway
Write-Host "Restarting Gateway..." -ForegroundColor Green
docker restart OcelotApiGateway | Out-Null

Start-Sleep -Seconds 10

Write-Host "`n=== Testing Services ===" -ForegroundColor Green

# Test individual APIs
Write-Host "Testing Admin API..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:$adminPort/swagger/v1/swagger.json" -TimeoutSec 10
    Write-Host "✓ Admin API: OK" -ForegroundColor Green
} catch {
    Write-Host "✗ Admin API: FAILED - $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Testing CIC API..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:$cicPort/swagger/v1/swagger.json" -TimeoutSec 10
    Write-Host "✓ CIC API: OK" -ForegroundColor Green
} catch {
    Write-Host "✗ CIC API: FAILED - $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Testing Gateway..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:$gatewayPort/health" -TimeoutSec 10
    Write-Host "✓ Gateway Health: OK" -ForegroundColor Green
} catch {
    Write-Host "✗ Gateway Health: FAILED - $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Final URLs ===" -ForegroundColor Green
Write-Host "Admin API: http://localhost:$adminPort/swagger" -ForegroundColor Cyan
Write-Host "CIC API: http://localhost:$cicPort/swagger" -ForegroundColor Cyan
Write-Host "Gateway: http://localhost:$gatewayPort/swagger" -ForegroundColor Cyan

Write-Host "`nThe Gateway should now work properly!" -ForegroundColor Green

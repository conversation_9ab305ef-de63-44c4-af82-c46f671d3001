# Simple Gateway Fix Script

Write-Host "=== Simple Gateway Fix ===" -ForegroundColor Green

# Get current container ports
$adminPort = (docker port CICPlatform.Admin.API 8080).Split(':')[1]
$cicPort = (docker port CICPlatform.API 8080).Split(':')[1]
$gatewayPort = (docker port OcelotApiGateway 8080).Split(':')[1]

Write-Host "Current ports:"
Write-Host "  Admin API: $adminPort"
Write-Host "  CIC API: $cicPort"
Write-Host "  Gateway: $gatewayPort"

# Use localhost as the host (this should work from the Gateway container to the host)
$hostAddress = "host.docker.internal"

# Create simple Swagger endpoints configuration
$swaggerConfig = @"
{
  "SwaggerEndPoints": [
    {
      "Key": "admin",
      "Config": [
        {
          "Name": "Admin Web API",
          "Version": "v1",
          "Url": "http://$hostAddress`:$adminPort/swagger/v1/swagger.json"
        }
      ]
    },
    {
      "Key": "cic",
      "Config": [
        {
          "Name": "CIC Web API", 
          "Version": "v1",
          "Url": "http://$hostAddress`:$cicPort/swagger/v1/swagger.json"
        }
      ]
    }
  ]
}
"@

Write-Host "Updating Swagger endpoints..."
$swaggerConfig | Set-Content "OcelotApiGateway\OcelotConfiguration\ocelot.SwaggerEndpoints.json"

# Update admin routes
Write-Host "Updating admin routes..."
$adminConfig = Get-Content "OcelotApiGateway\OcelotConfiguration\ocelot.admin.json" -Raw
$adminConfig = $adminConfig -replace '"Host": "[^"]*"', "`"Host`": `"$hostAddress`""
$adminConfig = $adminConfig -replace '"Port": \d+', "`"Port`": $adminPort"
$adminConfig | Set-Content "OcelotApiGateway\OcelotConfiguration\ocelot.admin.json"

# Update CIC routes
Write-Host "Updating CIC routes..."
$cicConfig = Get-Content "OcelotApiGateway\OcelotConfiguration\ocelot.cic.json" -Raw
$cicConfig = $cicConfig -replace '"Host": "[^"]*"', "`"Host`": `"$hostAddress`""
$cicConfig = $cicConfig -replace '"Port": \d+', "`"Port`": $cicPort"
$cicConfig | Set-Content "OcelotApiGateway\OcelotConfiguration\ocelot.cic.json"

Write-Host "Restarting Gateway..."
docker restart OcelotApiGateway

Start-Sleep -Seconds 10

Write-Host ""
Write-Host "=== URLs ==="
Write-Host "Admin API: http://localhost:$adminPort/swagger"
Write-Host "CIC API: http://localhost:$cicPort/swagger"
Write-Host "Gateway: http://localhost:$gatewayPort/swagger"
Write-Host ""
Write-Host "Gateway should now work!"

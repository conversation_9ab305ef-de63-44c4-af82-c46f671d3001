@echo off
echo ========================================
echo Fixing HTTPS Certificate Issues
echo ========================================

echo Step 1: Stopping existing containers to rebuild them...
docker stop OcelotApiGateway CICPlatform.API CICPlatform.Admin.API 2>nul

echo.
echo Step 2: Removing containers to force rebuild...
docker rm OcelotApiGateway CICPlatform.API CICPlatform.Admin.API 2>nul

echo.
echo ========================================
echo HTTPS has been completely disabled in:
echo - All launchSettings.json files
echo - All Dockerfile files  
echo - All Program.cs files
echo ========================================
echo.
echo Now you can:
echo 1. Run your services from Visual Studio again
echo 2. They will start with HTTP only (no HTTPS certificate needed)
echo 3. Access them using HTTP URLs only
echo.
echo Example URLs after restart:
echo - Admin API: http://localhost:[PORT]/swagger
echo - CIC API: http://localhost:[PORT]/swagger
echo - Gateway: http://localhost:[PORT]/swagger
echo.
echo (Replace [PORT] with the actual port shown in Visual Studio output)
echo ========================================

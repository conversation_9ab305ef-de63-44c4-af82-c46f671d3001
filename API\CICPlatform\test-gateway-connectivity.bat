@echo off
echo Testing Gateway connectivity to downstream services...
echo.

echo Current container ports:
docker ps --format "table {{.Names}}\t{{.Ports}}"
echo.

echo Testing direct access to Admin API Swagger JSON:
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:32808/swagger/v1/swagger.json' -TimeoutSec 5; Write-Host 'Admin API Swagger JSON: OK' -ForegroundColor Green } catch { Write-Host 'Admin API Swagger JSON: FAILED' -ForegroundColor Red; Write-Host $_.Exception.Message }"

echo.
echo Testing direct access to CIC API Swagger JSON:
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:32809/swagger/v1/swagger.json' -TimeoutSec 5; Write-Host 'CIC API Swagger JSON: OK' -ForegroundColor Green } catch { Write-Host 'CIC API Swagger JSON: FAILED' -ForegroundColor Red; Write-Host $_.Exception.Message }"

echo.
echo Testing Gateway health:
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:32810/health' -TimeoutSec 5; Write-Host 'Gateway Health: OK' -ForegroundColor Green } catch { Write-Host 'Gateway Health: FAILED' -ForegroundColor Red; Write-Host $_.Exception.Message }"

echo.
echo Testing Gateway Swagger docs endpoint:
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:32810/swagger/docs/v1/admin' -TimeoutSec 5; Write-Host 'Gateway Admin Swagger Docs: OK' -ForegroundColor Green } catch { Write-Host 'Gateway Admin Swagger Docs: FAILED' -ForegroundColor Red; Write-Host $_.Exception.Message }"

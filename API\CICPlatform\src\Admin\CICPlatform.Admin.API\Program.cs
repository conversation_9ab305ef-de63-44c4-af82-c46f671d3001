// Create and configure the builder
using CICPlatform.Admin.API.Configuration;
using CICPlatform.Admin.API.EndpointDefinitions;

// Completely disable HTTPS for Docker
Environment.SetEnvironmentVariable("ASPNETCORE_HTTPS_PORTS", "");
Environment.SetEnvironmentVariable("ASPNETCORE_FORWARDEDHEADERS_ENABLED", "false");
Environment.SetEnvironmentVariable("DOTNET_RUNNING_IN_CONTAINER", "true");

var builder = WebApplication.CreateBuilder(args);
builder.ConfigureBuilder();
builder.ConfigureServices();

// Build the application
var app = builder.Build();

// Configure the HTTP request pipeline
app.ConfigureApp();

// Register endpoints
UserEndpointDefinition.RegisterEndpoints(app);

// Start the application
app.Run();
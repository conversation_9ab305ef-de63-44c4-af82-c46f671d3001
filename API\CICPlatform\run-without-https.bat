@echo off
echo ========================================
echo Starting Services WITHOUT HTTPS
echo ========================================

echo Step 1: Stopping any existing containers...
docker stop OcelotApiGateway CICPlatform.API CICPlatform.Admin.API admin-api cic-api gateway 2>nul
docker rm OcelotApiGateway CICPlatform.API CICPlatform.Admin.API admin-api cic-api gateway 2>nul

echo.
echo Step 2: Creating Docker network...
docker network create cic-network 2>nul

echo.
echo Step 3: Building fresh images (this may take a few minutes)...
echo Building Admin API...
docker build -t cicplatformadminapi:latest -f "src/Admin/CICPlatform.Admin.API/Dockerfile" .

echo Building CIC API...
docker build -t cicplatformapi:latest -f "src/CICPlatform/CICPlatform.API/Dockerfile" .

echo Building Gateway...
docker build -t ocelotapigateway:latest -f "OcelotApiGateway/Dockerfile" .

echo.
echo Step 4: Starting services with HTTP only...

echo Starting Admin API on port 5002...
docker run -d --name admin-api ^
  --network cic-network ^
  -p 5002:8080 ^
  -e ASPNETCORE_ENVIRONMENT=Development ^
  -e ASPNETCORE_URLS=http://+:8080 ^
  -e ASPNETCORE_HTTP_PORTS=8080 ^
  -e ASPNETCORE_HTTPS_PORTS= ^
  -e DOTNET_RUNNING_IN_CONTAINER=true ^
  cicplatformadminapi:latest

echo Starting CIC API on port 5003...
docker run -d --name cic-api ^
  --network cic-network ^
  -p 5003:8080 ^
  -e ASPNETCORE_ENVIRONMENT=Development ^
  -e ASPNETCORE_URLS=http://+:8080 ^
  -e ASPNETCORE_HTTP_PORTS=8080 ^
  -e ASPNETCORE_HTTPS_PORTS= ^
  -e DOTNET_RUNNING_IN_CONTAINER=true ^
  cicplatformapi:latest

echo Waiting for APIs to start...
timeout /t 15 /nobreak >nul

echo Starting Gateway on port 5004...
docker run -d --name gateway ^
  --network cic-network ^
  -p 5004:8080 ^
  -e ASPNETCORE_ENVIRONMENT=Development ^
  -e ASPNETCORE_URLS=http://+:8080 ^
  -e ASPNETCORE_HTTP_PORTS=8080 ^
  -e ASPNETCORE_HTTPS_PORTS= ^
  -e DOTNET_RUNNING_IN_CONTAINER=true ^
  ocelotapigateway:latest

echo.
echo Step 5: Checking container status...
timeout /t 5 /nobreak >nul
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo.
echo ========================================
echo Services are now running on HTTP only:
echo - Admin API: http://localhost:5002/swagger
echo - CIC API: http://localhost:5003/swagger
echo - Gateway: http://localhost:5004/swagger
echo ========================================
echo.
echo Note: All HTTPS has been disabled to avoid certificate issues.
echo Use HTTP URLs only (not HTTPS).
echo.
echo To stop all services, run: docker stop admin-api cic-api gateway

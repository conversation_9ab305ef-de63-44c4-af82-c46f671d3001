// Create and configure the builder
using CICPlatform.API.Configuration;
using CICPlatform.API.EndpointDefinitions;

var builder = WebApplication.CreateBuilder(args);
builder.ConfigureBuilder(builder);
builder.ConfigureServices(builder);

// Build the application
var app = builder.Build();

// Configure the HTTP request pipeline
builder.ConfigureApp(app);

// Register endpoints
RevenueIncomeTypeEndpointDefinition.RegisterEndpoints(app);
RevenueIncomeEndpointDefinition.RegisterEndpoints(app);

// Start the application
app.Run();
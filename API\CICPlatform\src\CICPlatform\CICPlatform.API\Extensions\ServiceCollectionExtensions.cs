using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Security.Claims;

namespace CICPlatform.API.Extensions
{
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// Adds custom authentication services
        /// </summary>
        public static IServiceCollection AddCustomAuthentication(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment environment)
        {
            var logger = services.BuildServiceProvider().GetRequiredService<ILogger<Program>>();

            // Disable JWT claim type mapping to make it work with Azure AD default claims
            System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler.DefaultInboundClaimTypeMap.Clear();

            // Get configuration sections
            var jwtSettings = configuration.GetSection("JwtSettings");
            var azureAd = configuration.GetSection("AzureAd");
            var securitySettings = configuration.GetSection("Security");

            // Configure JWT Bearer authentication with Azure AD
            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.Authority = $"{azureAd["Instance"]}{azureAd["TenantId"]}";
                options.Audience = azureAd["Audience"];
                options.RequireHttpsMetadata = securitySettings.GetValue<bool>("RequireHttps", !environment.IsDevelopment());

                // Configure token validation parameters with environment-specific settings
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = jwtSettings.GetValue<bool>("ValidateIssuer", true),
                    ValidateAudience = jwtSettings.GetValue<bool>("ValidateAudience", !environment.IsDevelopment()),
                    ValidateLifetime = jwtSettings.GetValue<bool>("ValidateLifetime", true),
                    ValidateIssuerSigningKey = jwtSettings.GetValue<bool>("ValidateIssuerSigningKey", true),

                    ValidIssuer = jwtSettings["ValidIssuer"],

                    ValidAudiences = [azureAd["Audience"]],

                    ClockSkew = TimeSpan.Parse(jwtSettings["ClockSkew"] ?? "00:05:00"), // Environment-specific clock skew

                    NameClaimType = "name",
                    RoleClaimType = "roles" // Azure AD standard for roles
                };

                // Add comprehensive logging for debugging
                options.Events = new JwtBearerEvents
                {
                    OnAuthenticationFailed = context =>
                    {
                        logger.LogWarning("Authentication failed: {Exception}", context.Exception);
                        return Task.CompletedTask;
                    },
                    OnTokenValidated = context =>
                    {
                        logger.LogInformation("Token validated successfully");

                        // Log all claims in the token
                        if (context.Principal?.Identity is ClaimsIdentity identity)
                        {
                            logger.LogInformation("Claims in the token:");
                            foreach (var claim in identity.Claims)
                            {
                                logger.LogInformation("Claim: {type} = {value}", claim.Type, claim.Value);
                            }
                        }

                        return Task.CompletedTask;
                    }
                };
            });

            logger.LogInformation("Authentication services configured");
            return services;
        }

        /// <summary>
        /// Adds custom CORS services
        /// </summary>
        public static IServiceCollection AddCustomCors(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment environment)
        {
            var logger = services.BuildServiceProvider().GetRequiredService<ILogger<Program>>();

            // Add CORS with environment-specific origins
            services.AddCors(options =>
            {
                var allowedOrigins = configuration.GetSection("AllowedOrigins").Get<string[]>() ??
                    ["http://localhost:5004"];

                options.AddPolicy("DefaultPolicy", policy =>
                {
                    policy.WithOrigins(allowedOrigins)
                          .WithMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                          .WithHeaders("Authorization", "Content-Type", "Tenant-ID")
                          .SetIsOriginAllowed(origin => environment.IsDevelopment() || allowedOrigins.Contains(origin))
                          .AllowCredentials();
                });
            });

            logger.LogInformation("CORS services configured");
            return services;
        }
    }
}

@echo off
echo Stopping Visual Studio containers...
docker stop OcelotApiGateway CICPlatform.API CICPlatform.Admin.API 2>nul
docker rm OcelotApiGateway CICPlatform.API CICPlatform.Admin.API 2>nul

echo.
echo Creating Docker network...
docker network create cic-network 2>nul

echo.
echo Starting services with fixed ports and proper networking...

echo Starting Admin API...
docker run -d --name admin-api ^
  --network cic-network ^
  -p 5002:8080 ^
  -e ASPNETCORE_ENVIRONMENT=Development ^
  -e ASPNETCORE_URLS=http://+:8080 ^
  -e ASPNETCORE_HTTPS_PORTS= ^
  cicplatformadminapi:dev

echo Starting CIC API...
docker run -d --name cic-api ^
  --network cic-network ^
  -p 5003:8080 ^
  -e ASPNETCORE_ENVIRONMENT=Development ^
  -e ASPNETCORE_URLS=http://+:8080 ^
  -e ASPNETCORE_HTTPS_PORTS= ^
  cicplatformapi:dev

echo Waiting for APIs to start...
timeout /t 10 /nobreak >nul

echo Starting Gateway...
docker run -d --name gateway ^
  --network cic-network ^
  -p 5004:8080 ^
  -e ASPNETCORE_ENVIRONMENT=Development ^
  -e ASPNETCORE_URLS=http://+:8080 ^
  -e ASPNETCORE_HTTPS_PORTS= ^
  ocelotapigateway:dev

echo.
echo Services are now running on fixed ports:
echo - Admin API: http://localhost:5002/swagger
echo - CIC API: http://localhost:5003/swagger
echo - Gateway: http://localhost:5004/swagger
echo.

echo Checking container status...
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo.
echo All services should now be accessible and the Gateway should work properly!

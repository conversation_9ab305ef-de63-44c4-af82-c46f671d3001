{"Routes": [{"SwaggerKey": "admin", "AddClaimsToRequest": {}, "AddHeadersToRequest": {}, "AddQueriesToRequest": {}, "AuthenticationOptions": {"AllowedScopes": ["admin.read", "admin.write"], "AuthenticationProviderKey": "Bearer", "AuthenticationProviderKeys": []}, "ChangeDownstreamPathTemplate": {}, "DangerousAcceptAnyServerCertificateValidator": true, "DelegatingHandlers": [], "DownstreamHeaderTransform": {}, "DownstreamHostAndPorts": [{"Host": "admin-api", "Port": 8080}], "DownstreamHttpMethod": null, "DownstreamHttpVersion": null, "DownstreamHttpVersionPolicy": null, "DownstreamPathTemplate": "/api/{version}/{everything}", "DownstreamScheme": "http", "FileCacheOptions": {"TtlSeconds": 15, "Region": "adminregion", "Header": null, "EnableContentHashing": null}, "HttpHandlerOptions": {"AllowAutoRedirect": false, "MaxConnectionsPerServer": **********, "PooledConnectionLifetimeSeconds": null, "UseCookieContainer": false, "UseProxy": false, "UseTracing": false}, "Key": null, "LoadBalancerOptions": {"Expiry": **********, "Key": "", "Type": "RoundR<PERSON>in"}, "Metadata": {}, "Priority": 2, "QoSOptions": {"DurationOfBreak": 10000, "ExceptionsAllowedBeforeBreaking": 3, "TimeoutValue": 5000}, "RateLimitOptions": {"ClientWhitelist": [], "EnableRateLimiting": false, "Limit": 0, "Period": null, "PeriodTimespan": 0.0}, "RequestIdKey": null, "RouteClaimsRequirement": {"role": "Admin"}, "RouteIsCaseSensitive": false, "SecurityOptions": {"IPAllowedList": [], "IPBlockedList": [], "ExcludeAllowedFromBlocked": false}, "ServiceName": null, "ServiceNamespace": null, "Timeout": 0, "UpstreamHeaderTemplates": {}, "UpstreamHeaderTransform": {}, "UpstreamHost": null, "UpstreamHttpMethod": ["Get", "Post", "Put", "Delete"], "UpstreamPathTemplate": "/admin-gate/{version}/{everything}"}, {"SwaggerKey": "admin", "AddClaimsToRequest": {}, "AddHeadersToRequest": {}, "AddQueriesToRequest": {}, "AuthenticationOptions": {"AllowedScopes": [], "AuthenticationProviderKey": null, "AuthenticationProviderKeys": []}, "ChangeDownstreamPathTemplate": {}, "DangerousAcceptAnyServerCertificateValidator": false, "DelegatingHandlers": [], "DownstreamHeaderTransform": {}, "DownstreamHostAndPorts": [{"Host": "admin-api", "Port": 8080}], "DownstreamHttpMethod": null, "DownstreamHttpVersion": null, "DownstreamHttpVersionPolicy": null, "DownstreamPathTemplate": "/swagger/{everything}", "DownstreamScheme": "http", "FileCacheOptions": {"TtlSeconds": null, "Region": null, "Header": null, "EnableContentHashing": null}, "HttpHandlerOptions": {"AllowAutoRedirect": false, "MaxConnectionsPerServer": **********, "PooledConnectionLifetimeSeconds": null, "UseCookieContainer": false, "UseProxy": false, "UseTracing": false}, "Key": null, "LoadBalancerOptions": {"Expiry": **********, "Key": "", "Type": ""}, "Metadata": {}, "Priority": 1, "QoSOptions": {"DurationOfBreak": 1, "ExceptionsAllowedBeforeBreaking": 0, "TimeoutValue": 0}, "RateLimitOptions": {"ClientWhitelist": [], "EnableRateLimiting": false, "Limit": 0, "Period": null, "PeriodTimespan": 0.0}, "RequestIdKey": null, "RouteClaimsRequirement": {}, "RouteIsCaseSensitive": false, "SecurityOptions": {"IPAllowedList": [], "IPBlockedList": [], "ExcludeAllowedFromBlocked": false}, "ServiceName": null, "ServiceNamespace": null, "Timeout": 0, "UpstreamHeaderTemplates": {}, "UpstreamHeaderTransform": {}, "UpstreamHost": null, "UpstreamHttpMethod": ["Get", "Options"], "UpstreamPathTemplate": "/admin-swagger/{everything}"}, {"SwaggerKey": "cic", "AddClaimsToRequest": {}, "AddHeadersToRequest": {}, "AddQueriesToRequest": {}, "AuthenticationOptions": {"AllowedScopes": ["cic.read", "cic.write"], "AuthenticationProviderKey": "Bearer", "AuthenticationProviderKeys": []}, "ChangeDownstreamPathTemplate": {}, "DangerousAcceptAnyServerCertificateValidator": false, "DelegatingHandlers": [], "DownstreamHeaderTransform": {}, "DownstreamHostAndPorts": [{"Host": "cic-api", "Port": 8080}], "DownstreamHttpMethod": null, "DownstreamHttpVersion": null, "DownstreamHttpVersionPolicy": null, "DownstreamPathTemplate": "/api/{version}/{everything}", "DownstreamScheme": "http", "FileCacheOptions": {"TtlSeconds": 15, "Region": "cicregion", "Header": null, "EnableContentHashing": null}, "HttpHandlerOptions": {"AllowAutoRedirect": false, "MaxConnectionsPerServer": **********, "PooledConnectionLifetimeSeconds": null, "UseCookieContainer": false, "UseProxy": false, "UseTracing": false}, "Key": null, "LoadBalancerOptions": {"Expiry": **********, "Key": "", "Type": "RoundR<PERSON>in"}, "Metadata": {}, "Priority": 2, "QoSOptions": {"DurationOfBreak": 10000, "ExceptionsAllowedBeforeBreaking": 3, "TimeoutValue": 5000}, "RateLimitOptions": {"ClientWhitelist": [], "EnableRateLimiting": false, "Limit": 0, "Period": null, "PeriodTimespan": 0.0}, "RequestIdKey": null, "RouteClaimsRequirement": {"role": "User"}, "RouteIsCaseSensitive": false, "SecurityOptions": {"IPAllowedList": [], "IPBlockedList": [], "ExcludeAllowedFromBlocked": false}, "ServiceName": null, "ServiceNamespace": null, "Timeout": 0, "UpstreamHeaderTemplates": {}, "UpstreamHeaderTransform": {}, "UpstreamHost": null, "UpstreamHttpMethod": ["Get", "Post", "Put", "Delete", "Options"], "UpstreamPathTemplate": "/cic-gate/{version}/{everything}"}, {"SwaggerKey": "cic", "AddClaimsToRequest": {}, "AddHeadersToRequest": {}, "AddQueriesToRequest": {}, "AuthenticationOptions": {"AllowedScopes": [], "AuthenticationProviderKey": null, "AuthenticationProviderKeys": []}, "ChangeDownstreamPathTemplate": {}, "DangerousAcceptAnyServerCertificateValidator": false, "DelegatingHandlers": [], "DownstreamHeaderTransform": {}, "DownstreamHostAndPorts": [{"Host": "cic-api", "Port": 8080}], "DownstreamHttpMethod": null, "DownstreamHttpVersion": null, "DownstreamHttpVersionPolicy": null, "DownstreamPathTemplate": "/swagger/{everything}", "DownstreamScheme": "http", "FileCacheOptions": {"TtlSeconds": null, "Region": null, "Header": null, "EnableContentHashing": null}, "HttpHandlerOptions": {"AllowAutoRedirect": false, "MaxConnectionsPerServer": **********, "PooledConnectionLifetimeSeconds": null, "UseCookieContainer": false, "UseProxy": false, "UseTracing": false}, "Key": null, "LoadBalancerOptions": {"Expiry": **********, "Key": "", "Type": ""}, "Metadata": {}, "Priority": 1, "QoSOptions": {"DurationOfBreak": 1, "ExceptionsAllowedBeforeBreaking": 0, "TimeoutValue": 0}, "RateLimitOptions": {"ClientWhitelist": [], "EnableRateLimiting": false, "Limit": 0, "Period": null, "PeriodTimespan": 0.0}, "RequestIdKey": null, "RouteClaimsRequirement": {}, "RouteIsCaseSensitive": false, "SecurityOptions": {"IPAllowedList": [], "IPBlockedList": [], "ExcludeAllowedFromBlocked": false}, "ServiceName": null, "ServiceNamespace": null, "Timeout": 0, "UpstreamHeaderTemplates": {}, "UpstreamHeaderTransform": {}, "UpstreamHost": null, "UpstreamHttpMethod": ["Get", "Options"], "UpstreamPathTemplate": "/cic-swagger/{everything}"}], "DynamicRoutes": [], "Aggregates": [], "GlobalConfiguration": {"BaseUrl": "http://ocelot-gateway:8080", "CacheOptions": {"TtlSeconds": null, "Region": null, "Header": null, "EnableContentHashing": null}, "DownstreamHttpVersion": "2.0", "DownstreamHttpVersionPolicy": null, "DownstreamScheme": null, "HttpHandlerOptions": {"AllowAutoRedirect": false, "MaxConnectionsPerServer": **********, "PooledConnectionLifetimeSeconds": null, "UseCookieContainer": false, "UseProxy": false, "UseTracing": true}, "LoadBalancerOptions": {"Expiry": **********, "Key": "", "Type": ""}, "MetadataOptions": {"CurrentCulture": "", "NumberStyle": "Any", "Separators": [","], "StringSplitOption": "None", "TrimChars": [" "], "Metadata": {}}, "QoSOptions": {"DurationOfBreak": 10000, "ExceptionsAllowedBeforeBreaking": 3, "TimeoutValue": 5000}, "RateLimitOptions": {"ClientIdHeader": "ClientId", "DisableRateLimitHeaders": false, "HttpStatusCode": 429, "QuotaExceededMessage": "API rate limit exceeded", "RateLimitCounterPrefix": "ocelot"}, "RequestIdKey": null, "SecurityOptions": {"IPAllowedList": [], "IPBlockedList": [], "ExcludeAllowedFromBlocked": false}, "ServiceDiscoveryProvider": {"Scheme": null, "Host": null, "Port": 0, "Type": "AppConfiguration", "Token": null, "ConfigurationKey": null, "PollingInterval": 0, "Namespace": null}}, "SwaggerEndPoints": [{"Key": "admin", "VersionPlaceholder": "{version}", "KeyToPath": "admin", "Config": [{"Name": "Admin Web API", "Version": "v1", "Url": "http://admin-api:8080/swagger/v1/swagger.json", "Service": null}], "HostOverride": null, "TransformByOcelotConfig": true, "RemoveUnusedComponentsFromScheme": true, "TakeServersFromDownstreamService": false}, {"Key": "cic", "VersionPlaceholder": "{version}", "KeyToPath": "cic", "Config": [{"Name": "CIC Web API", "Version": "v1", "Url": "http://cic-api:8080/swagger/v1/swagger.json", "Service": null}], "HostOverride": null, "TransformByOcelotConfig": true, "RemoveUnusedComponentsFromScheme": true, "TakeServersFromDownstreamService": false}]}
{"ConnectionStrings": {"Default": "Server=10.20.4.88;Port=5432;Database=techskill;Username=techskill;Password=**************"}, "DatabaseSettings": {"DefaultDB": "PostgreSQL"}, "AzureAd": {"Instance": "https://login.microsoftonline.com/", "Domain": "pannapps.co", "TenantId": "93708259-3510-4cf8-bfab-b0802727f5e4", "ClientId": "c0356a99-ac1b-4857-92db-1fcfc1c47d81", "Audience": "c0356a99-ac1b-4857-92db-1fcfc1c47d81", "AudienceURI": "api://c0356a99-ac1b-4857-92db-1fcfc1c47d81", "CallbackPath": "/signin-oidc", "SignedOutCallbackPath": "/signout-callback-oidc"}, "JwtSettings": {"ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "ValidIssuer": "https://login.microsoftonline.com/93708259-3510-4cf8-bfab-b0802727f5e4/v2.0"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "AllowedOrigins": ["http://localhost:5004"]}
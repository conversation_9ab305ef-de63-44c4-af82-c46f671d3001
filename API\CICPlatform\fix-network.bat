@echo off
echo Creating a shared Docker network for the services...

REM Create a custom network
docker network create cic-network 2>nul

echo Connecting existing containers to the network...

REM Connect all containers to the same network
docker network connect cic-network CICPlatform.Admin.API 2>nul
docker network connect cic-network CICPlatform.API 2>nul
docker network connect cic-network OcelotApiGateway 2>nul

echo Network setup complete.

echo.
echo Testing connectivity...
echo Admin API: http://localhost:32796/swagger
echo CIC API: http://localhost:32798/swagger
echo Gateway: http://localhost:32804/swagger

echo.
echo Containers should now be able to communicate with each other.
echo Try accessing the Gateway at: http://localhost:32804/swagger

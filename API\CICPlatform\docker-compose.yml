version: '3.8'

services:
  admin-api:
    build:
      context: .
      dockerfile: src/Admin/CICPlatform.Admin.API/Dockerfile
    container_name: admin-api
    ports:
      - "5002:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__Default=Server=10.20.4.88;Port=5432;Database=techskill;Username=techskill;Password=**************;Include Error Detail=true;
    networks:
      - cic-network
    restart: on-failure

  cic-api:
    build:
      context: .
      dockerfile: src/CICPlatform/CICPlatform.API/Dockerfile
    container_name: cic-api
    ports:
      - "5003:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__Default=Server=10.20.4.88;Port=5432;Database=techskill;Username=techskill;Password=**************;Include Error Detail=true;
    networks:
      - cic-network
    restart: on-failure

  ocelot-gateway:
    build:
      context: .
      dockerfile: OcelotApiGateway/Dockerfile
    container_name: ocelot-gateway
    ports:
      - "5004:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - Services__admin__DownstreamPath=http://admin-api:8080
      - Services__cic__DownstreamPath=http://cic-api:8080
    networks:
      - cic-network
    depends_on:
      - admin-api
      - cic-api
    restart: on-failure

networks:
  cic-network:
    driver: bridge

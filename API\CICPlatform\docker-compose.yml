# Docker Compose configuration

services:
  admin-api:
    image: cicplatformadminapi:dev
    container_name: admin-api
    ports:
      - "5002:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__Default=Server=10.20.4.88;Port=5432;Database=techskill;Username=techskill;Password=**************;Include Error Detail=true;
    networks:
      - cic-network
    restart: on-failure

  cic-api:
    image: cicplatformapi:dev
    container_name: cic-api
    ports:
      - "5003:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__Default=Server=10.20.4.88;Port=5432;Database=techskill;Username=techskill;Password=**************;Include Error Detail=true;
    networks:
      - cic-network
    restart: on-failure

  ocelot-gateway:
    image: ocelotapigateway:dev
    container_name: ocelot-gateway
    ports:
      - "5004:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - Services__admin__DownstreamPath=http://admin-api:8080
      - Services__cic__DownstreamPath=http://cic-api:8080
    networks:
      - cic-network
    depends_on:
      - admin-api
      - cic-api
    restart: on-failure

networks:
  cic-network:
    driver: bridge

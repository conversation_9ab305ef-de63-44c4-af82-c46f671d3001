# PowerShell script to automatically fix Gateway configuration with current container ports

Write-Host "=== Auto-fixing Gateway Configuration ===" -ForegroundColor Green

# Get current container ports
try {
    $adminPortOutput = docker port CICPlatform.Admin.API 8080 2>$null
    $cicPortOutput = docker port C<PERSON>Platform.API 8080 2>$null
    $gatewayPortOutput = docker port OcelotApiGateway 8080 2>$null

    if (-not $adminPortOutput -or -not $cicPortOutput -or -not $gatewayPortOutput) {
        Write-Host "Error: One or more containers are not running!" -ForegroundColor Red
        Write-Host "Please start all containers from Visual Studio first." -ForegroundColor Yellow
        exit 1
    }

    $adminPort = $adminPortOutput.Split(':')[1]
    $cicPort = $cicPortOutput.Split(':')[1]
    $gatewayPort = $gatewayPortOutput.Split(':')[1]

    Write-Host "Detected ports:" -ForegroundColor Yellow
    Write-Host "  Admin API: $adminPort" -ForegroundColor Cyan
    Write-Host "  CIC API: $cicPort" -ForegroundColor Cyan
    Write-Host "  Gateway: $gatewayPort" -ForegroundColor Cyan

    # Update admin configuration
    Write-Host "`nUpdating admin configuration..." -ForegroundColor Green
    $adminConfigPath = "OcelotApiGateway\OcelotConfiguration\ocelot.admin.json"
    $adminConfig = Get-Content $adminConfigPath -Raw
    $adminConfig = $adminConfig -replace '"Port": \d+', "`"Port`": $adminPort"
    $adminConfig | Set-Content $adminConfigPath

    # Update CIC configuration
    Write-Host "Updating CIC configuration..." -ForegroundColor Green
    $cicConfigPath = "OcelotApiGateway\OcelotConfiguration\ocelot.cic.json"
    $cicConfig = Get-Content $cicConfigPath -Raw
    $cicConfig = $cicConfig -replace '"Port": \d+', "`"Port`": $cicPort"
    $cicConfig | Set-Content $cicConfigPath

    # Update Swagger endpoints
    Write-Host "Updating Swagger endpoints..." -ForegroundColor Green
    $swaggerConfigPath = "OcelotApiGateway\OcelotConfiguration\ocelot.SwaggerEndpoints.json"
    $swaggerConfig = Get-Content $swaggerConfigPath -Raw
    
    # Replace admin port
    $swaggerConfig = $swaggerConfig -replace 'host\.docker\.internal:\d+/swagger/v1/swagger\.json"', "host.docker.internal:$adminPort/swagger/v1/swagger.json`""
    
    # Replace CIC port (need to be more specific for the second occurrence)
    $lines = $swaggerConfig -split "`n"
    for ($i = 0; $i -lt $lines.Length; $i++) {
        if ($lines[$i] -match "CIC Web API" -and $i + 2 -lt $lines.Length) {
            $lines[$i + 2] = $lines[$i + 2] -replace 'host\.docker\.internal:\d+', "host.docker.internal:$cicPort"
            break
        }
    }
    $swaggerConfig = $lines -join "`n"
    $swaggerConfig | Set-Content $swaggerConfigPath

    # Restart Gateway
    Write-Host "Restarting Gateway..." -ForegroundColor Green
    docker restart OcelotApiGateway | Out-Null

    Start-Sleep -Seconds 8

    Write-Host "`n=== Configuration Updated Successfully! ===" -ForegroundColor Green
    Write-Host "`nYour services are now available at:" -ForegroundColor Yellow
    Write-Host "  Admin API: http://localhost:$adminPort/swagger" -ForegroundColor Cyan
    Write-Host "  CIC API: http://localhost:$cicPort/swagger" -ForegroundColor Cyan
    Write-Host "  Gateway: http://localhost:$gatewayPort/swagger" -ForegroundColor Cyan

    Write-Host "`nTesting Gateway connectivity..." -ForegroundColor Green
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$gatewayPort/health" -TimeoutSec 10 -ErrorAction Stop
        Write-Host "✓ Gateway health check: OK" -ForegroundColor Green
    } catch {
        Write-Host "✗ Gateway health check: FAILED" -ForegroundColor Red
        Write-Host "  Error: $($_.Exception.Message)" -ForegroundColor Red
    }

    Write-Host "`nTesting Swagger endpoints..." -ForegroundColor Green
    try {
        $adminSwagger = Invoke-WebRequest -Uri "http://localhost:$adminPort/swagger/v1/swagger.json" -TimeoutSec 10 -ErrorAction Stop
        Write-Host "✓ Admin Swagger JSON: OK" -ForegroundColor Green
    } catch {
        Write-Host "✗ Admin Swagger JSON: FAILED" -ForegroundColor Red
    }

    try {
        $cicSwagger = Invoke-WebRequest -Uri "http://localhost:$cicPort/swagger/v1/swagger.json" -TimeoutSec 10 -ErrorAction Stop
        Write-Host "✓ CIC Swagger JSON: OK" -ForegroundColor Green
    } catch {
        Write-Host "✗ CIC Swagger JSON: FAILED" -ForegroundColor Red
    }

} catch {
    Write-Host "Error occurred: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

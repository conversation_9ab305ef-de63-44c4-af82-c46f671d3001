<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.16" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.16" />
		<!-- <PackageReference Include="Microsoft.Identity.Web" Version="2.17.1" />
		<PackageReference Include="Microsoft.Identity.Web.UI" Version="2.17.1" /> -->
		<PackageReference Include="MMLib.Ocelot.Provider.AppConfiguration" Version="4.0.0" />
		<PackageReference Include="MMLib.SwaggerForOcelot" Version="9.0.0" />
		<PackageReference Include="Ocelot" Version="24.0.0" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.1" />
		<PackageReference Include="AspNetCore.HealthChecks.Uris" Version="8.0.0" />
		<PackageReference Include="Ocelot.Cache.CacheManager" Version="24.0.0" />
		<PackageReference Include="Ocelot.Provider.Polly" Version="24.0.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="8.0.1" />
	</ItemGroup>

	<Target Name="DeletePDBFiles" AfterTargets="Publish">
		<ItemGroup>
			<FilesToDelete Include="$(PublishDir)*.pdb" />
		</ItemGroup>
		<Delete Files="@(FilesToDelete)">
			<Output TaskParameter="DeletedFiles" ItemName="FilesDeleted" />
		</Delete>
		<Message Text="Deleted PDB files: @(FilesDeleted)" Importance="high" />
	</Target>
	<Target Name="DeleteAppSettingsFiles" AfterTargets="Publish">
		<ItemGroup>
			<FilesToDelete Include="$(PublishDir)appsettings*.json" Exclude="$(PublishDir)appsettings.json;$(PublishDir)appsettings.$(Mode).json" />
		</ItemGroup>
		<Delete Files="@(FilesToDelete)">
			<Output TaskParameter="DeletedFiles" ItemName="FilesDeleted" />
		</Delete>
		<Message Text="Deleted appsettings files: @(FilesDeleted)" Importance="high" />
	</Target>

</Project>
